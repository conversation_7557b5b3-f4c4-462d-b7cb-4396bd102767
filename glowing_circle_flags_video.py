#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فيديو قصير بصيغة TikTok/YouTube Shorts مع دائرة مضيئة وأعلام متحركة
Short TikTok/YouTube Shorts video with glowing circle and animated flags
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from moviepy.editor import VideoClip, AudioFileClip, CompositeVideoClip, TextClip
import math
import os
import requests
from io import BytesIO

class GlowingCircleFlagsVideo:
    def __init__(self):
        self.width = 1080
        self.height = 1920  # 9:16 aspect ratio for TikTok/YouTube Shorts
        self.duration = 25  # 25 seconds
        self.fps = 30
        
        # Colors
        self.bg_color = (10, 10, 20)  # Dark background
        self.glow_color = (100, 200, 255)  # Blue glow
        self.text_color = (255, 255, 255)  # White text
        
        # Circle properties
        self.circle_radius = 300
        self.circle_center = (self.width // 2, self.height // 2)
        
        # Flag URLs (using emoji flags as placeholders)
        self.flags = {
            'china': '🇨🇳',
            'russia': '🇷🇺', 
            'turkey': '🇹🇷',
            'usa': '🇺🇸'
        }
        
    def create_glowing_circle(self, t):
        """Create a glowing circle that rotates slowly"""
        img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Rotation angle based on time
        rotation = (t * 30) % 360  # 30 degrees per second
        
        # Create multiple circles for glow effect
        for i in range(10):
            alpha = int(255 * (1 - i/10) * 0.3)
            radius = self.circle_radius + i * 5
            
            # Calculate circle position with slight rotation
            offset_x = math.cos(math.radians(rotation)) * 2
            offset_y = math.sin(math.radians(rotation)) * 2
            
            center_x = self.circle_center[0] + offset_x
            center_y = self.circle_center[1] + offset_y
            
            # Draw circle
            bbox = [
                center_x - radius,
                center_y - radius,
                center_x + radius,
                center_y + radius
            ]
            
            color = (*self.glow_color, alpha)
            draw.ellipse(bbox, outline=color, width=3)
        
        return img
    
    def create_flag_emoji(self, flag_emoji, size=80):
        """Create flag image from emoji"""
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        try:
            # Try to load a font that supports emoji
            font = ImageFont.truetype("seguiemj.ttf", size-10)
        except:
            try:
                font = ImageFont.truetype("NotoColorEmoji.ttf", size-10)
            except:
                font = ImageFont.load_default()
        
        # Draw emoji
        bbox = draw.textbbox((0, 0), flag_emoji, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2
        
        draw.text((x, y), flag_emoji, font=font, fill=(255, 255, 255, 255))
        
        return img
    
    def position_flags(self, t):
        """Calculate flag positions based on time"""
        # Animation progress (0 to 1)
        progress = min(t / (self.duration * 0.8), 1.0)
        
        # Easing function for smooth animation
        eased_progress = 1 - (1 - progress) ** 3
        
        # Initial positions (far apart)
        left_start_x = self.circle_center[0] - 200
        right_start_x = self.circle_center[0] + 200
        
        # Final positions (closer together)
        left_end_x = self.circle_center[0] - 100
        right_end_x = self.circle_center[0] + 100
        
        # Interpolate positions
        left_x = left_start_x + (left_end_x - left_start_x) * eased_progress
        right_x = right_start_x + (right_end_x - right_start_x) * eased_progress
        
        # Y positions for flags
        flag_y = self.circle_center[1]
        
        positions = {
            'left_group': {
                'china': (left_x - 60, flag_y - 60),
                'russia': (left_x, flag_y),
                'turkey': (left_x - 60, flag_y + 60)
            },
            'right_group': {
                'usa': (right_x, flag_y)
            }
        }
        
        return positions
    
    def create_frame(self, t):
        """Create a single frame of the video"""
        # Create background
        img = Image.new('RGB', (self.width, self.height), self.bg_color)
        
        # Add glowing circle
        circle_img = self.create_glowing_circle(t)
        img.paste(circle_img, (0, 0), circle_img)
        
        # Get flag positions
        positions = self.position_flags(t)
        
        # Add flags
        for group_name, group_flags in positions.items():
            for flag_name, pos in group_flags.items():
                flag_emoji = self.flags[flag_name]
                flag_img = self.create_flag_emoji(flag_emoji)
                
                # Add glow effect to flags
                glow_img = flag_img.filter(ImageFilter.GaussianBlur(radius=3))
                
                # Paste flag with glow
                paste_x = int(pos[0] - flag_img.width // 2)
                paste_y = int(pos[1] - flag_img.height // 2)
                
                if 0 <= paste_x < self.width - flag_img.width and 0 <= paste_y < self.height - flag_img.height:
                    img.paste(glow_img, (paste_x, paste_y), glow_img)
                    img.paste(flag_img, (paste_x, paste_y), flag_img)
        
        return np.array(img)
    
    def create_background_effects(self, t):
        """Create animated background effects"""
        img = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Animated particles
        for i in range(20):
            # Particle position based on time and index
            angle = (t * 50 + i * 18) % 360
            radius = 400 + math.sin(t * 2 + i) * 50

            x = self.circle_center[0] + math.cos(math.radians(angle)) * radius
            y = self.circle_center[1] + math.sin(math.radians(angle)) * radius

            # Particle size and alpha
            size = 3 + math.sin(t * 3 + i) * 2
            alpha = int(100 + math.sin(t * 4 + i) * 50)

            if 0 <= x < self.width and 0 <= y < self.height:
                draw.ellipse([x-size, y-size, x+size, y+size],
                           fill=(*self.glow_color, alpha))

        return img

    def create_video(self):
        """Create the complete video"""
        print("إنشاء فيديو الدائرة المضيئة والأعلام...")
        print("Creating glowing circle and flags video...")

        # Create enhanced frame function
        def enhanced_frame(t):
            # Create background
            img = Image.new('RGB', (self.width, self.height), self.bg_color)

            # Add background effects
            bg_effects = self.create_background_effects(t)
            img.paste(bg_effects, (0, 0), bg_effects)

            # Add glowing circle
            circle_img = self.create_glowing_circle(t)
            img.paste(circle_img, (0, 0), circle_img)

            # Get flag positions
            positions = self.position_flags(t)

            # Add flags with enhanced effects
            for group_name, group_flags in positions.items():
                for flag_name, pos in group_flags.items():
                    flag_emoji = self.flags[flag_name]
                    flag_img = self.create_flag_emoji(flag_emoji, size=100)

                    # Add multiple glow layers
                    for glow_radius in [5, 3, 1]:
                        glow_img = flag_img.filter(ImageFilter.GaussianBlur(radius=glow_radius))
                        paste_x = int(pos[0] - flag_img.width // 2)
                        paste_y = int(pos[1] - flag_img.height // 2)

                        if 0 <= paste_x < self.width - flag_img.width and 0 <= paste_y < self.height - flag_img.height:
                            img.paste(glow_img, (paste_x, paste_y), glow_img)

                    # Paste main flag
                    paste_x = int(pos[0] - flag_img.width // 2)
                    paste_y = int(pos[1] - flag_img.height // 2)

                    if 0 <= paste_x < self.width - flag_img.width and 0 <= paste_y < self.height - flag_img.height:
                        img.paste(flag_img, (paste_x, paste_y), flag_img)

            return np.array(img)

        # Create video clip
        video_clip = VideoClip(enhanced_frame, duration=self.duration)
        video_clip = video_clip.set_fps(self.fps)

        # Add animated title text
        def make_text_glow(t):
            # Pulsing glow effect
            glow_intensity = 0.7 + 0.3 * math.sin(t * 4)
            return f"rgba(100, 200, 255, {glow_intensity})"

        title_text = TextClip(
            "Which country will win?",
            fontsize=70,
            color='white',
            font='Arial-Bold',
            stroke_color='black',
            stroke_width=3
        ).set_position(('center', 150)).set_duration(self.duration)

        # Add multiple glow layers for text
        title_glow1 = TextClip(
            "Which country will win?",
            fontsize=75,
            color='lightblue',
            font='Arial-Bold'
        ).set_position(('center', 148)).set_duration(self.duration).set_opacity(0.6)

        title_glow2 = TextClip(
            "Which country will win?",
            fontsize=80,
            color='cyan',
            font='Arial-Bold'
        ).set_position(('center', 146)).set_duration(self.duration).set_opacity(0.3)

        # Composite video with multiple layers
        final_video = CompositeVideoClip([
            video_clip,
            title_glow2,
            title_glow1,
            title_text
        ])

        # Export video
        output_path = "glowing_circle_flags_tiktok.mp4"
        print(f"حفظ الفيديو في: {output_path}")
        print(f"Saving video to: {output_path}")

        final_video.write_videofile(
            output_path,
            fps=self.fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            ffmpeg_params=['-crf', '20']  # Higher quality
        )

        print("تم إنشاء الفيديو بنجاح!")
        print("Video created successfully!")

        return output_path

def main():
    """Main function to create the video"""
    try:
        creator = GlowingCircleFlagsVideo()
        output_file = creator.create_video()
        print(f"\nالفيديو جاهز: {output_file}")
        print(f"Video ready: {output_file}")
        print(f"المدة: {creator.duration} ثانية")
        print(f"Duration: {creator.duration} seconds")
        print(f"الدقة: {creator.width}x{creator.height} (9:16)")
        print(f"Resolution: {creator.width}x{creator.height} (9:16)")
        
    except Exception as e:
        print(f"خطأ في إنشاء الفيديو: {e}")
        print(f"Error creating video: {e}")

if __name__ == "__main__":
    main()
