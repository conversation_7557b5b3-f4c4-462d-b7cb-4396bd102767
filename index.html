<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صانع التغريدات - Tweet Creator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <style>
        /* إضافة خط ثانوي أنيق */
        @import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700&display=swap');

        body {
            font-family: '<PERSON><PERSON>','<PERSON><PERSON><PERSON>', sans-serif;
            background: linear-gradient(135deg,#667eea 0%,#764ba2 50%,#f093fb 100%);
            min-height: 100vh;
        }
        
        .tweet-container {
            background: #fff;
            border: 1px solid #e1e8ed;
            border-radius: 16px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .tweet-container:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }
        
        .verified-badge {
            color: #1da1f2;
        }
        
        .character-count {
            transition: color 0.3s ease;
        }
        
        .character-count.warning {
            color: #ff9800;
        }
        
        .character-count.danger {
            color: #f44336;
        }
        
        .custom-file-input {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }
        
        .custom-file-input input[type=file] {
            position: absolute;
            left: -9999px;
        }
        
        .custom-file-label {
            display: inline-block;
            padding: 8px 12px;
            background: #f3f4f6;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .custom-file-label:hover {
            background: #e5e7eb;
            border-color: #9ca3af;
        }
        
        .preview-image {
            max-width: 100%;
            border-radius: 12px;
            margin-top: 8px;
        }
        
        .theme-light { background: #fff; }
        .theme-dark { background: #192734; color: #fff; }
        .theme-dim { background: #15202b; color: #fff; }
        
        .animate-pulse-custom {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }
        
        .floating-tools {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
        }
        
        .floating-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #1da1f2;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(29, 161, 242, 0.4);
            transition: all 0.3s ease;
        }
        
        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(29, 161, 242, 0.6);
        }
        
        .emoji-picker {
            display: none;
            position: absolute;
            bottom: 70px;
            left: 0;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            padding: 16px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(8, 1fr);
            gap: 8px;
        }
        
        .emoji-item {
            cursor: pointer;
            font-size: 24px;
            padding: 4px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }
        
        .emoji-item:hover {
            background-color: #f3f4f6;
        }
    </style>
</head>
<body class="p-4">
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">صانع التغريدات</h1>
            <p class="text-white/80 text-lg">أنشئ تغريدة مخصصة بكل سهولة</p>
        </div>

        <div class="grid lg:grid-cols-3 gap-8">
            <!-- Control Panel -->
            <div class="lg:col-span-1 bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl">
                <h2 class="text-2xl font-bold mb-4 text-gray-800">لوحة التحكم</h2>
                
                <!-- Theme Selector -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">السمة</label>
                    <select id="themeSelector" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="light">فاتح</option>
                        <option value="dark">داكن</option>
                        <option value="dim">مظلم</option>
                    </select>
                </div>

                <!-- Name Input -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الاسم</label>
                    <input type="text" id="userName" placeholder="أدخل اسمك" 
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                           value="أحمد محمد">
                </div>

                <!-- Username Input -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                    <input type="text" id="userHandle" placeholder="@username" 
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                           value="@ahmed_dev">
                </div>

                <!-- Profile Picture -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الصورة الشخصية</label>
                    <div class="custom-file-input">
                        <input type="file" id="profilePic" accept="image/*">
                        <label for="profilePic" class="custom-file-label text-center">
                            <span>اختر صورة</span>
                        </label>
                    </div>
                    <img id="profilePreview" class="preview-image hidden" alt="Profile Preview">
                </div>

                <!-- Verified Badge -->
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="verifiedBadge" checked class="mr-2">
                        <span class="text-sm font-medium text-gray-700">علامة التحقق</span>
                    </label>
                </div>

                <!-- Tweet Content -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">نص التغريدة</label>
                    <textarea id="tweetContent" rows="4" 
                              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 resize-none"
                              placeholder="ما الذي يدور في بالك؟">أطلقت للتو مشروعي الجديد! 🚀
متحمس جداً لمشاركته معكم قريباً. 
#تطوير #تقنية #ابداع</textarea>
                    <div class="text-left">
                        <span id="charCount" class="text-sm text-gray-500">0/280</span>
                    </div>
                </div>

                <!-- Media Upload -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">إضافة صورة أو فيديو</label>
                    <div class="custom-file-input">
                        <input type="file" id="mediaUpload" accept="image/*,video/*">
                        <label for="mediaUpload" class="custom-file-label text-center">
                            <span>اختر ملف</span>
                        </label>
                    </div>
                    <div id="mediaPreview" class="mt-2"></div>
                </div>

                <!-- Stats -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">الإحصائيات</label>
                    <div class="grid grid-cols-3 gap-2">
                        <input type="number" id="replies" placeholder="الردود" 
                               class="p-2 border border-gray-300 rounded-lg" value="42">
                        <input type="number" id="retweets" placeholder="إعادات التغريد" 
                               class="p-2 border border-gray-300 rounded-lg" value="128">
                        <input type="number" id="likes" placeholder="الإعجابات" 
                               class="p-2 border border-gray-300 rounded-lg" value="1.2">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-2">
                    <button onclick="downloadTweet()" 
                            class="w-full bg-blue-500 text-white py-3 rounded-lg hover:bg-blue-600 transition duration-200">
                        تحميل كصورة
                    </button>
                    <button onclick="shareTweet()" 
                            class="w-full bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition duration-200">
                        مشاركة الصورة
                    </button>
                    <button onclick="resetTweet()" 
                            class="w-full bg-gray-500 text-white py-3 rounded-lg hover:bg-gray-600 transition duration-200">
                        إعادة تعيين
                    </button>
                </div>
            </div>

            <!-- Tweet Preview -->
            <div class="lg:col-span-2">
                <h2 class="text-2xl font-bold mb-4 text-white text-center">معاينة التغريدة</h2>
                <div id="tweetPreview" class="tweet-container theme-light">
                    <div class="p-4">
                        <!-- Header -->
                        <div class="flex items-start">
                            <img id="previewProfilePic" 
                                 src="https://via.placeholder.com/48x48" 
                                 class="w-12 h-12 rounded-full mr-3">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <span id="previewName" class="font-bold text-black">أحمد محمد</span>
                                    <span id="previewVerified" class="ml-1 verified-badge">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                </div>
                                <span id="previewHandle" class="text-gray-500 text-sm">@ahmed_dev</span>
                                <div class="text-gray-500 text-sm">· 2h</div>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="mt-3">
                            <p id="previewContent" class="text-black leading-relaxed"></p>
                        </div>

                        <!-- Media -->
                        <div id="previewMedia" class="mt-3"></div>

                        <!-- Stats -->
                        <div class="flex justify-around mt-4 pt-4 border-t border-gray-200">
                            <div class="flex items-center text-gray-500">
                                <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm4 0H9v2h2V9zm4 0h-2v2h2V9z" clip-rule="evenodd"></path>
                                </svg>
                                <span id="previewReplies">42</span>
                            </div>
                            <div class="flex items-center text-gray-500">
                                <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-5L9 2H4z" clip-rule="evenodd"></path>
                                </svg>
                                <span id="previewRetweets">128</span>
                            </div>
                            <div class="flex items-center text-gray-500">
                                <svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                </svg>
                                <span id="previewLikes">1.2K</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Tools -->
    <div class="floating-tools">
        <div class="floating-btn" onclick="toggleEmojiPicker()">
            😊
        </div>
        <div id="emojiPicker" class="emoji-picker">
            <div class="emoji-grid">
                <span class="emoji-item" onclick="addEmoji('😀')">😀</span>
                <span class="emoji-item" onclick="addEmoji('😍')">😍</span>
                <span class="emoji-item" onclick="addEmoji('🎉')">🎉</span>
                <span class="emoji-item" onclick="addEmoji('🔥')">🔥</span>
                <span class="emoji-item" onclick="addEmoji('💙')">💙</span>
                <span class="emoji-item" onclick="addEmoji('✨')">✨</span>
                <span class="emoji-item" onclick="addEmoji('🚀')">🚀</span>
                <span class="emoji-item" onclick="addEmoji('🌟')">🌟</span>
                <span class="emoji-item" onclick="addEmoji('📸')">📸</span>
                <span class="emoji-item" onclick="addEmoji('🎯')">🎯</span>
                <span class="emoji-item" onclick="addEmoji('💡')">💡</span>
                <span class="emoji-item" onclick="addEmoji('🎨')">🎨</span>
                <span class="emoji-item" onclick="addEmoji('💪')">💪</span>
                <span class="emoji-item" onclick="addEmoji('🙌')">🙌</span>
                <span class="emoji-item" onclick="addEmoji('🤝')">🤝</span>
                <span class="emoji-item" onclick="addEmoji('❤️')">❤️</span>
            </div>
        </div>
    </div>

    <script>
        // Initialize
        updatePreview();

        // Event listeners
        document.getElementById('userName').addEventListener('input', updatePreview);
        document.getElementById('userHandle').addEventListener('input', updatePreview);
        document.getElementById('tweetContent').addEventListener('input', updatePreview);
        document.getElementById('verifiedBadge').addEventListener('change', updatePreview);
        document.getElementById('themeSelector').addEventListener('change', updateTheme);
        document.getElementById('replies').addEventListener('input', updateStats);
        document.getElementById('retweets').addEventListener('input', updateStats);
        document.getElementById('likes').addEventListener('input', updateStats);
        document.getElementById('profilePic').addEventListener('change', handleProfilePic);
        document.getElementById('mediaUpload').addEventListener('change', handleMediaUpload);

        function updatePreview() {
            const name = document.getElementById('userName').value;
            const handle = document.getElementById('userHandle').value;
            const content = document.getElementById('tweetContent').value;
            const verified = document.getElementById('verifiedBadge').checked;
            const charCount = document.getElementById('charCount');
            
            // Update character count
            const count = content.length;
            charCount.textContent = `${count}/280`;
            
            if (count > 260) {
                charCount.className = 'text-sm f44336 danger';
            } else if (count > 240) {
                charCount.className = 'text-sm warning';
            } else {
                charCount.className = 'text-sm text-gray-500';
            }
            
            // Update preview
            document.getElementById('previewName').textContent = name;
            document.getElementById('previewHandle').textContent = handle;
            document.getElementById('previewContent').innerHTML = content.replace(/\n/g, '<br>');
            document.getElementById('previewVerified').style.display = verified ? 'inline-block' : 'none';
            
            updateStats();
        }

        function updateStats() {
            const replies = document.getElementById('replies').value;
            const retweets = document.getElementById('retweets').value;
            const likes = document.getElementById('likes').value;
            
            document.getElementById('previewReplies').textContent = formatNumber(replies);
            document.getElementById('previewRetweets').textContent = formatNumber(retweets);
            document.getElementById('previewLikes').textContent = formatNumber(likes);
        }

        function formatNumber(num) {
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num;
        }

        function updateTheme() {
            const theme = document.getElementById('themeSelector').value;
            const tweet = document.getElementById('tweetPreview');
            
            tweet.className = `tweet-container theme-${theme}`;
            
            if (theme === 'dark' || theme === 'dim') {
                document.getElementById('previewName').className = 'font-bold text-white';
                document.getElementById('previewContent').className = 'text-white leading-relaxed';
            } else {
                document.getElementById('previewName').className = 'font-bold text-black';
                document.getElementById('previewContent').className = 'text-black leading-relaxed';
            }
        }

        function handleProfilePic(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewProfilePic').src = e.target.result;
                    document.getElementById('profilePreview').src = e.target.result;
                    document.getElementById('profilePreview').classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            }
        }

        function handleMediaUpload(event) {
            const file = event.target.files[0];
            const preview = document.getElementById('previewMedia');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (file.type.startsWith('image/')) {
                        preview.innerHTML = `<img src="${e.target.result}" class="w-full rounded-lg">`;
                    } else if (file.type.startsWith('video/')) {
                        preview.innerHTML = `<video src="${e.target.result}" class="w-full rounded-lg" controls></video>`;
                    }
                };
                reader.readAsDataURL(file);
            }
        }

        async function downloadTweet() {
            const tweet = document.getElementById('tweetPreview');
            const canvas = await html2canvas(tweet, {backgroundColor: null, scale: 2});
            canvas.toBlob(blob => {
                const link = document.createElement('a');
                link.download = 'tweet.png';
                link.href = URL.createObjectURL(blob);
                link.click();
            });
        }

        async function shareTweet() {
            const tweet = document.getElementById('tweetPreview');
            const canvas = await html2canvas(tweet, {backgroundColor: null, scale: 2});
            canvas.toBlob(async blob => {
                const file = new File([blob], 'tweet.png', {type: 'image/png'});
                if (navigator.share && navigator.canShare({files: [file]})) {
                    try {
                        await navigator.share({
                            title: 'تغريدتي',
                            text: 'شاهد تغريدتي المصممة!',
                            files: [file]
                        });
                    } catch (err) {
                        alert('لم يتمكن من المشاركة، جاري التحميل بدلاً...');
                        downloadTweet();
                    }
                } else {
                    downloadTweet(); // fallback
                }
            });
        }

        function resetTweet() {
            document.getElementById('userName').value = 'أحمد محمد';
            document.getElementById('userHandle').value = '@ahmed_dev';
            document.getElementById('tweetContent').value = 'أطلقت للتو مشروعي الجديد! 🚀\nمتحمس جداً لمشاركته معكم قريباً. \n#تطوير #تقنية #ابداع';
            document.getElementById('verifiedBadge').checked = true;
            document.getElementById('themeSelector').value = 'light';
            document.getElementById('replies').value = '42';
            document.getElementById('retweets').value = '128';
            document.getElementById('likes').value = '1.2';
            document.getElementById('previewProfilePic').src = 'https://via.placeholder.com/48x48';
            document.getElementById('previewMedia').innerHTML = '';
            
            updatePreview();
            updateTheme();
        }

        function toggleEmojiPicker() {
            const picker = document.getElementById('emojiPicker');
            picker.style.display = picker.style.display === 'block' ? 'none' : 'block';
        }

        function addEmoji(emoji) {
            const content = document.getElementById('tweetContent');
            content.value += emoji;
            updatePreview();
            toggleEmojiPicker();
        }

        // Close emoji picker when clicking outside
        document.addEventListener('click', function(event) {
            const picker = document.getElementById('emojiPicker');
            const btn = document.querySelector('.floating-btn');
            if (!picker.contains(event.target) && !btn.contains(event.target)) {
                picker.style.display = 'none';
            }
        });
    </script>
</body>
</html>