// بيانات التغريدات التجريبية
const tweetsData = [
    {
        id: 1,
        name: "أحمد محمد",
        username: "@ahmed_dev",
        avatar: "https://via.placeholder.com/48x48/1DA1F2/ffffff?text=أ",
        verified: true,
        time: "2h",
        content: "أطلقت للتو مشروعي الجديد في تطوير المواقع! 🚀 متحمس جداً لمشاركته معكم قريباً. #تطوير #تقنية #ابداع",
        image: "https://via.placeholder.com/500x300/1DA1F2/ffffff?text=مشروع+جديد",
        replies: 42,
        retweets: 128,
        likes: 1200
    },
    {
        id: 2,
        name: "فاطمة علي",
        username: "@fatima_design",
        avatar: "https://via.placeholder.com/48x48/E1306C/ffffff?text=ف",
        verified: true,
        time: "4h",
        content: "التصميم ليس مجرد شكل جميل، بل حل لمشكلة حقيقية. كل عنصر في التصميم يجب أن يخدم هدفاً واضحاً ✨ #تصميم #UX #UI",
        replies: 67,
        retweets: 89,
        likes: 543
    },
    {
        id: 3,
        name: "محمد الأحمد",
        username: "@mohammed_tech",
        avatar: "https://via.placeholder.com/48x48/00D084/ffffff?text=م",
        verified: false,
        time: "6h",
        content: "نصيحة لكل مطور مبتدئ: لا تخف من الأخطاء، فهي جزء من رحلة التعلم. كل خطأ يعلمك شيئاً جديداً 💪 #برمجة #تعلم #تطوير",
        replies: 23,
        retweets: 45,
        likes: 234
    },
    {
        id: 4,
        name: "سارة حسن",
        username: "@sara_writer",
        avatar: "https://via.placeholder.com/48x48/8B5CF6/ffffff?text=س",
        verified: true,
        time: "8h",
        content: "الكتابة فن يحتاج إلى صبر وممارسة مستمرة. كل كلمة لها وزنها وكل جملة لها إيقاعها 📝 #كتابة #أدب #إبداع",
        image: "https://via.placeholder.com/500x250/8B5CF6/ffffff?text=فن+الكتابة",
        replies: 34,
        retweets: 67,
        likes: 456
    },
    {
        id: 5,
        name: "عبدالله خالد",
        username: "@abdullah_biz",
        avatar: "https://via.placeholder.com/48x48/F59E0B/ffffff?text=ع",
        verified: false,
        time: "12h",
        content: "ريادة الأعمال ليست مجرد فكرة، بل تنفيذ وإصرار وتعلم من الفشل. النجاح يأتي لمن يثابر 🎯 #ريادة_أعمال #نجاح #إلهام",
        replies: 56,
        retweets: 123,
        likes: 789
    },
    {
        id: 6,
        name: "نور الدين",
        username: "@noor_photo",
        avatar: "https://via.placeholder.com/48x48/EF4444/ffffff?text=ن",
        verified: true,
        time: "1d",
        content: "التصوير لغة عالمية تتحدث بالضوء والظلال. كل صورة تحكي قصة وكل لقطة تحمل مشاعر 📸 #تصوير #فن #إبداع",
        image: "https://via.placeholder.com/500x400/EF4444/ffffff?text=عدسة+المصور",
        replies: 78,
        retweets: 156,
        likes: 923
    }
];

// تحميل التغريدات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadTweets();
    setupComposeArea();
});

// تحميل التغريدات
function loadTweets() {
    const container = document.getElementById('tweetsContainer');
    container.innerHTML = '';
    
    tweetsData.forEach(tweet => {
        const tweetElement = createTweetElement(tweet);
        container.appendChild(tweetElement);
    });
}

// إنشاء عنصر التغريدة
function createTweetElement(tweet) {
    const tweetDiv = document.createElement('div');
    tweetDiv.className = 'tweet';
    tweetDiv.innerHTML = `
        <div class="tweet-header">
            <img src="${tweet.avatar}" alt="${tweet.name}" class="tweet-avatar">
            <div class="tweet-user-info">
                <div class="tweet-user-name">
                    <span class="user-name">${tweet.name}</span>
                    ${tweet.verified ? '<i class="fas fa-check-circle verified-badge"></i>' : ''}
                    <span class="user-handle">${tweet.username}</span>
                    <span class="tweet-time">· ${tweet.time}</span>
                </div>
            </div>
        </div>
        <div class="tweet-content">${tweet.content}</div>
        ${tweet.image ? `<div class="tweet-media"><img src="${tweet.image}" alt="صورة التغريدة"></div>` : ''}
        <div class="tweet-actions">
            <button class="action-btn reply-btn" onclick="replyToTweet(${tweet.id})">
                <i class="far fa-comment"></i>
                <span>${formatNumber(tweet.replies)}</span>
            </button>
            <button class="action-btn retweet-btn" onclick="retweetTweet(${tweet.id})">
                <i class="fas fa-retweet"></i>
                <span>${formatNumber(tweet.retweets)}</span>
            </button>
            <button class="action-btn like-btn" onclick="likeTweet(${tweet.id})">
                <i class="far fa-heart"></i>
                <span>${formatNumber(tweet.likes)}</span>
            </button>
            <button class="action-btn share-btn" onclick="shareTweet(${tweet.id})">
                <i class="fas fa-share"></i>
            </button>
        </div>
    `;
    
    return tweetDiv;
}

// تنسيق الأرقام
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// إعداد منطقة كتابة التغريدة
function setupComposeArea() {
    const composeInput = document.querySelector('.compose-input');
    const tweetBtn = document.querySelector('.tweet-btn');
    
    composeInput.addEventListener('input', function() {
        const text = this.value.trim();
        tweetBtn.disabled = text.length === 0 || text.length > 280;
        
        if (text.length > 280) {
            this.style.borderColor = '#f91880';
        } else {
            this.style.borderColor = 'transparent';
        }
    });
    
    tweetBtn.addEventListener('click', function() {
        const text = composeInput.value.trim();
        if (text && text.length <= 280) {
            addNewTweet(text);
            composeInput.value = '';
            tweetBtn.disabled = true;
        }
    });
}

// إضافة تغريدة جديدة
function addNewTweet(content) {
    const newTweet = {
        id: Date.now(),
        name: "أنت",
        username: "@you",
        avatar: "https://via.placeholder.com/48x48/1DA1F2/ffffff?text=أ",
        verified: false,
        time: "الآن",
        content: content,
        replies: 0,
        retweets: 0,
        likes: 0
    };
    
    tweetsData.unshift(newTweet);
    
    const container = document.getElementById('tweetsContainer');
    const tweetElement = createTweetElement(newTweet);
    container.insertBefore(tweetElement, container.firstChild);
    
    // تأثير بصري للتغريدة الجديدة
    tweetElement.style.backgroundColor = 'rgba(29, 155, 240, 0.1)';
    setTimeout(() => {
        tweetElement.style.backgroundColor = '';
    }, 2000);
}

// وظائف التفاعل مع التغريدات
function replyToTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    if (tweet) {
        const composeInput = document.querySelector('.compose-input');
        composeInput.value = `@${tweet.username.substring(1)} `;
        composeInput.focus();
        composeInput.setSelectionRange(composeInput.value.length, composeInput.value.length);
    }
}

function retweetTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    const button = event.target.closest('.retweet-btn');
    
    if (tweet && button) {
        if (!button.classList.contains('retweeted')) {
            tweet.retweets++;
            button.classList.add('retweeted');
            button.querySelector('span').textContent = formatNumber(tweet.retweets);
            
            // تأثير بصري
            button.style.transform = 'scale(1.2)';
            setTimeout(() => {
                button.style.transform = '';
            }, 200);
        }
    }
}

function likeTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    const button = event.target.closest('.like-btn');
    
    if (tweet && button) {
        if (!button.classList.contains('liked')) {
            tweet.likes++;
            button.classList.add('liked');
            button.querySelector('i').className = 'fas fa-heart';
            button.querySelector('span').textContent = formatNumber(tweet.likes);
            
            // تأثير بصري
            button.style.transform = 'scale(1.2)';
            setTimeout(() => {
                button.style.transform = '';
            }, 200);
        } else {
            tweet.likes--;
            button.classList.remove('liked');
            button.querySelector('i').className = 'far fa-heart';
            button.querySelector('span').textContent = formatNumber(tweet.likes);
        }
    }
}

function shareTweet(tweetId) {
    const tweet = tweetsData.find(t => t.id === tweetId);
    if (tweet) {
        if (navigator.share) {
            navigator.share({
                title: `تغريدة من ${tweet.name}`,
                text: tweet.content,
                url: window.location.href
            });
        } else {
            // نسخ الرابط إلى الحافظة
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('تم نسخ الرابط إلى الحافظة!');
            });
        }
    }
}
